const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game elements
const menu = document.getElementById('menu');
const gameModeSelection = document.getElementById('game-mode-selection');
const optionsMenu = document.getElementById('options-menu');
const highscoreScreen = document.getElementById('highscore-screen');
const gameArea = document.getElementById('game-area');
const gameOverScreen = document.getElementById('game-over');

// Buttons
const playBtn = document.getElementById('play-btn');
const optionsBtn = document.getElementById('options-btn');
const highscoreBtn = document.getElementById('highscore-btn');
const classicModeBtn = document.getElementById('classic-mode-btn');
const timeTrialModeBtn = document.getElementById('time-trial-mode-btn');
const obstaclesModeBtn = document.getElementById('obstacles-mode-btn');
const restartBtn = document.getElementById('restart-btn');
const backToMenuBtn = document.getElementById('back-to-menu-btn');
const backToMenuFromOptionsBtn = document.getElementById('back-to-menu-from-options-btn');
const backToMenuFromHighscoreBtn = document.getElementById('back-to-menu-from-highscore-btn');
const speedBtns = document.querySelectorAll('.speed-btn');

// Score and timer elements
const scoreDisplay = document.getElementById('score');
const highScoreDisplay = document.getElementById('high-score');
const finalScoreDisplay = document.getElementById('final-score');
const timerDisplay = document.getElementById('timer');
const highscoreList = document.getElementById('highscore-list');

// Audio elements
const eatSound = document.getElementById('eat-sound');
const gameOverSound = document.getElementById('game-over-sound');
const clickSound = document.getElementById('click-sound');

const box = 20;
let snake = [];
let food = {};
let obstacles = [];
let score = 0;
let highScores = JSON.parse(localStorage.getItem('highScores')) || [];
let gameSpeed = localStorage.getItem('gameSpeed') || 100;
let d;
let game;
let gameMode;
let timer;
let timeLeft;

// Event Listeners
playBtn.addEventListener('click', showGameModeSelection);
optionsBtn.addEventListener('click', showOptionsMenu);
highscoreBtn.addEventListener('click', showHighscoreScreen);
classicModeBtn.addEventListener('click', () => startGame('classic'));
timeTrialModeBtn.addEventListener('click', () => startGame('time-trial'));
obstaclesModeBtn.addEventListener('click', () => startGame('obstacles'));
restartBtn.addEventListener('click', () => {
    gameOverScreen.classList.add('hidden');
    showGameModeSelection();
});
backToMenuBtn.addEventListener('click', hideGameModeSelection);
backToMenuFromOptionsBtn.addEventListener('click', hideOptionsMenu);
backToMenuFromHighscoreBtn.addEventListener('click', hideHighscoreScreen);
document.addEventListener('keydown', direction);
speedBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        gameSpeed = btn.dataset.speed;
        localStorage.setItem('gameSpeed', gameSpeed);
        updateSpeedButtonUI();
    });
});

const allButtons = document.querySelectorAll('button');
allButtons.forEach(button => {
    button.addEventListener('click', () => {
        clickSound.currentTime = 0;
        clickSound.play();
    });
});

function init() {
    menu.classList.remove('hidden');
    gameModeSelection.classList.add('hidden');
    optionsMenu.classList.add('hidden');
    highscoreScreen.classList.add('hidden');
    gameArea.classList.add('hidden');
    gameOverScreen.classList.add('hidden');
    updateHighScoreDisplay();
    updateSpeedButtonUI();
}

function updateHighScoreDisplay() {
    const topScore = highScores.length > 0 ? highScores[0] : 0;
    highScoreDisplay.innerText = `High Score: ${topScore}`;
}

function updateSpeedButtonUI() {
    speedBtns.forEach(btn => {
        if (btn.dataset.speed == gameSpeed) {
            btn.style.backgroundColor = '#c0392b';
        } else {
            btn.style.backgroundColor = '#e74c3c';
        }
    });
}

function showGameModeSelection() {
    menu.classList.add('hidden');
    gameModeSelection.classList.remove('hidden');
}

function hideGameModeSelection() {
    gameModeSelection.classList.add('hidden');
    menu.classList.remove('hidden');
}

function showOptionsMenu() {
    menu.classList.add('hidden');
    optionsMenu.classList.remove('hidden');
}

function hideOptionsMenu() {
    optionsMenu.classList.add('hidden');
    menu.classList.remove('hidden');
}

function showHighscoreScreen() {
    menu.classList.add('hidden');
    highscoreScreen.classList.remove('hidden');
    populateHighscoreList();
}

function hideHighscoreScreen() {
    highscoreScreen.classList.add('hidden');
    menu.classList.remove('hidden');
}

function startGame(mode) {
    gameMode = mode;
    gameModeSelection.classList.add('hidden');
    gameArea.classList.remove('hidden');
    resetGame();
    game = setInterval(draw, gameSpeed);
}

function resetGame() {
    snake = [];
    snake[0] = { x: 9 * box, y: 10 * box };
    d = null;
    score = 0;
    scoreDisplay.innerText = `Score: ${score}`;
    spawnFood();

    if (gameMode === 'time-trial') {
        timeLeft = 60;
        timerDisplay.classList.remove('hidden');
        timerDisplay.innerText = `Time: ${timeLeft}`;
        timer = setInterval(updateTimer, 1000);
    } else {
        timerDisplay.classList.add('hidden');
    }

    if (gameMode === 'obstacles') {
        createObstacles();
    }
}

function updateTimer() {
    timeLeft--;
    timerDisplay.innerText = `Time: ${timeLeft}`;
    if (timeLeft <= 0) {
        clearInterval(game);
        clearInterval(timer);
        showGameOver();
    }
}

function createObstacles() {
    obstacles = [];
    for (let i = 0; i < 5; i++) {
        obstacles.push({
            x: Math.floor(Math.random() * 20) * box,
            y: Math.floor(Math.random() * 20) * box
        });
    }
}

function spawnFood() {
    food = {
        x: Math.floor(Math.random() * 20) * box,
        y: Math.floor(Math.random() * 20) * box
    }
}

function direction(event) {
    if (event.keyCode == 37 && d != 'RIGHT') {
        d = 'LEFT';
    } else if (event.keyCode == 38 && d != 'DOWN') {
        d = 'UP';
    } else if (event.keyCode == 39 && d != 'LEFT') {
        d = 'RIGHT';
    } else if (event.keyCode == 40 && d != 'UP') {
        d = 'DOWN';
    }
}

function collision(head, array) {
    for (let i = 0; i < array.length; i++) {
        if (head.x == array[i].x && head.y == array[i].y) {
            return true;
        }
    }
    return false;
}

function draw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    for (let i = 0; i < snake.length; i++) {
        ctx.fillStyle = (i == 0) ? '#2ecc71' : '#27ae60';
        ctx.fillRect(snake[i].x, snake[i].y, box, box);
        ctx.strokeStyle = '#16a085';
        ctx.strokeRect(snake[i].x, snake[i].y, box, box);

        // Draw snake head with eyes
        if (i == 0) {
            ctx.fillStyle = 'white';
            ctx.beginPath();
            if (d == 'LEFT') {
                ctx.arc(snake[i].x + box / 4, snake[i].y + box / 4, 2, 0, Math.PI * 2);
                ctx.arc(snake[i].x + box / 4, snake[i].y + box * 3 / 4, 2, 0, Math.PI * 2);
            } else if (d == 'UP') {
                ctx.arc(snake[i].x + box / 4, snake[i].y + box / 4, 2, 0, Math.PI * 2);
                ctx.arc(snake[i].x + box * 3 / 4, snake[i].y + box / 4, 2, 0, Math.PI * 2);
            } else if (d == 'RIGHT') {
                ctx.arc(snake[i].x + box * 3 / 4, snake[i].y + box / 4, 2, 0, Math.PI * 2);
                ctx.arc(snake[i].x + box * 3 / 4, snake[i].y + box * 3 / 4, 2, 0, Math.PI * 2);
            } else if (d == 'DOWN') {
                ctx.arc(snake[i].x + box / 4, snake[i].y + box * 3 / 4, 2, 0, Math.PI * 2);
                ctx.arc(snake[i].x + box * 3 / 4, snake[i].y + box * 3 / 4, 2, 0, Math.PI * 2);
            }
            ctx.fill();
        }
    }

    // Draw food as a circle
    ctx.fillStyle = '#f39c12'; // More vibrant orange
    ctx.beginPath();
    ctx.arc(food.x + box / 2, food.y + box / 2, box / 2, 0, Math.PI * 2);
    ctx.fill();

    if (gameMode === 'obstacles') {
        ctx.fillStyle = '#8e44ad';
        for (let i = 0; i < obstacles.length; i++) {
            ctx.fillRect(obstacles[i].x, obstacles[i].y, box, box);
        }
    }

    let snakeX = snake[0].x;
    let snakeY = snake[0].y;

    if (d == 'LEFT') snakeX -= box;
    if (d == 'UP') snakeY -= box;
    if (d == 'RIGHT') snakeX += box;
    if (d == 'DOWN') snakeY += box;

    if (snakeX == food.x && snakeY == food.y) {
        score++;
        scoreDisplay.innerText = `Score: ${score}`;
        eatSound.currentTime = 0;
        eatSound.play();
        spawnFood();
    } else {
        if (snake.length > 0) {
            snake.pop();
        }
    }

    let newHead = {
        x: snakeX,
        y: snakeY
    }

    if (snakeX < 0 || snakeX >= canvas.width || snakeY < 0 || snakeY >= canvas.height || collision(newHead, snake) || (gameMode === 'obstacles' && collision(newHead, obstacles))) {
        clearInterval(game);
        if (gameMode === 'time-trial') {
            clearInterval(timer);
        }
        showGameOver();
    }

    snake.unshift(newHead);
}

function showGameOver() {
    gameArea.classList.add('hidden');
    gameOverScreen.classList.remove('hidden');
    finalScoreDisplay.innerText = score;
    addScoreToHighscores(score);
    gameOverSound.currentTime = 0;
    gameOverSound.play();
}

init();