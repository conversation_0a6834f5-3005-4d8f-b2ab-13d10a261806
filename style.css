body {
    background: linear-gradient(to right, #6a11cb, #2575fc);
    color: #ecf0f1;
    font-family: 'Poppins', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
}

#game-container {
    background-color: rgba(0, 0, 0, 0.3);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px); /* Added blur effect */
}

h1 {
    color: #f1c40f;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

#menu, #game-mode-selection, #options-menu, #highscore-screen, #game-area, #game-over {
    transition: opacity 0.5s ease-in-out;
}

.hidden {
    opacity: 0;
    pointer-events: none;
    display: none;
}

#menu button, #game-mode-selection button, #options-menu button, #highscore-screen button {
    background: linear-gradient(to right, #f1c40f, #f39c12);
    color: #2c3e50;
    border: none;
    padding: 12px 24px;
    margin: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#menu button:hover, #game-mode-selection button:hover, #options-menu button:hover, #highscore-screen button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

#options-menu .option {
    margin-bottom: 20px;
}

#options-menu .option span {
    margin-right: 15px;
    font-size: 20px;
}

#highscore-list {
    list-style-type: none;
    padding: 0;
    margin-bottom: 20px;
}

#highscore-list li {
    font-size: 20px;
    margin-bottom: 8px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 5px;
    border-radius: 5px;
}

#game-area {
    position: relative;
}

canvas {
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid #f1c40f;
    border-radius: 10px;
    /* Added a subtle grid pattern */
    background-image: linear-gradient(0deg, transparent 9%, rgba(255,255,255,.1) 10%, rgba(255,255,255,.1) 11%, transparent 12%, transparent 89%, rgba(255,255,255,.1) 90%, rgba(255,255,255,.1) 91%, transparent 92%),
                      linear-gradient(90deg, transparent 9%, rgba(255,255,255,.1) 10%, rgba(255,255,255,.1) 11%, transparent 12%, transparent 89%, rgba(255,255,255,.1) 90%, rgba(255,255,255,.1) 91%, transparent 92%);
    background-size: 20px 20px; /* Matches box size */
}

#score, #high-score, #timer {
    font-size: 20px;
    margin-top: 15px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

#game-over {
    color: #f1c40f;
}

#game-over h2 {
    font-size: 36px;
}

#game-over p {
    font-size: 24px;
}

#game-over button {
    background: linear-gradient(to right, #f1c40f, #f39c12);
    color: #2c3e50;
    border: none;
    padding: 12px 24px;
    margin-top: 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#game-over button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}